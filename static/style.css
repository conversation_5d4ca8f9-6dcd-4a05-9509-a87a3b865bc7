:root {
    --primary-color: #4F46E5;
    --primary-color-light: #6366F1;
    --primary-color-dark: #3730A3;
    --secondary-color: #10B981;
    --secondary-color-light: #34D399;
    --accent-color: #F59E0B;
    --text-color: #1F2937;
    --text-color-light: #6B7280;
    --text-color-lighter: #9CA3AF;
    --background-color: #F8FAFC;
    --background-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    --container-background: #FFFFFF;
    --border-color: #E5E7EB;
    --border-color-light: #F3F4F6;
    --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
    --radius-sm: 0.375rem;
    --radius-md: 0.5rem;
    --radius-lg: 0.75rem;
    --radius-xl: 1rem;
}

* {
    box-sizing: border-box;
}

body {
    font-family: 'Inter', sans-serif;
    background: var(--background-color);
    margin: 0;
    padding: 0;
    color: var(--text-color);
    line-height: 1.6;
    min-height: 100vh;
}

/* Header Styles */
.header {
    background: var(--container-background);
    border-bottom: 1px solid var(--border-color);
    padding: 1rem 0;
    position: sticky;
    top: 0;
    z-index: 100;
    box-shadow: var(--shadow-sm);
}

.header-content {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 2rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.logo-container {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.logo {
    width: 48px;
    height: 48px;
    object-fit: contain;
}

.brand-title {
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--text-color);
    margin: 0;
}

.header-badge {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    background: linear-gradient(135deg, var(--primary-color), var(--primary-color-light));
    color: white;
    padding: 0.5rem 1rem;
    border-radius: var(--radius-lg);
    font-size: 0.875rem;
    font-weight: 600;
    box-shadow: var(--shadow-md);
}

/* Hero Section */
.hero {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-color-light) 100%);
    color: white;
    padding: 4rem 0;
    text-align: center;
}

.hero-content {
    max-width: 800px;
    margin: 0 auto;
    padding: 0 2rem;
}

.hero-title {
    font-size: 3rem;
    font-weight: 800;
    margin: 0 0 1rem 0;
    line-height: 1.2;
}

.hero-subtitle {
    font-size: 1.25rem;
    opacity: 0.9;
    margin: 0 0 3rem 0;
    font-weight: 400;
}

.features-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 2rem;
    margin-top: 3rem;
}

.feature-item {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.75rem;
    background: rgba(255, 255, 255, 0.1);
    padding: 1rem;
    border-radius: var(--radius-lg);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.feature-item i {
    font-size: 1.25rem;
}

.feature-item span {
    font-weight: 600;
}

/* Main Container */
.main-container {
    max-width: 1400px;
    margin: 0 auto;
    padding: 3rem 2rem;
}

.app-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 3rem;
    align-items: start;
}

/* Section Styles */
.upload-section, .results-section {
    background: var(--container-background);
    border-radius: var(--radius-xl);
    box-shadow: var(--shadow-lg);
    border: 1px solid var(--border-color);
    overflow: hidden;
}

.section-header {
    padding: 2rem 2rem 1rem 2rem;
    border-bottom: 1px solid var(--border-color-light);
    background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
}

.section-header h3 {
    font-size: 1.5rem;
    font-weight: 700;
    margin: 0 0 0.5rem 0;
    color: var(--text-color);
    display: flex;
    align-items: center;
    gap: 0.75rem;
}

.section-header h3 i {
    color: var(--primary-color);
}

.section-header p {
    margin: 0;
    color: var(--text-color-light);
    font-size: 0.95rem;
}

/* Upload Area */
.upload-area {
    padding: 2rem;
}

.file-upload-zone {
    display: block;
    border: 2px dashed var(--border-color);
    border-radius: var(--radius-lg);
    padding: 3rem 2rem;
    text-align: center;
    cursor: pointer;
    transition: all 0.3s ease;
    background: linear-gradient(135deg, #fafbfc 0%, #f8fafc 100%);
    position: relative;
    overflow: hidden;
}

.file-upload-zone::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(79, 70, 229, 0.1), transparent);
    transition: left 0.5s ease;
}

.file-upload-zone:hover {
    border-color: var(--primary-color);
    background: linear-gradient(135deg, #f0f4ff 0%, #e0e7ff 100%);
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
}

.file-upload-zone:hover::before {
    left: 100%;
}

.file-upload-zone.drag-over {
    border-color: var(--primary-color);
    background: linear-gradient(135deg, #f0f4ff 0%, #e0e7ff 100%);
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
}

.upload-content {
    position: relative;
    z-index: 1;
}

.upload-icon {
    font-size: 3rem;
    color: var(--primary-color);
    margin-bottom: 1rem;
    display: block;
}

.upload-content h4 {
    font-size: 1.25rem;
    font-weight: 600;
    margin: 0 0 0.5rem 0;
    color: var(--text-color);
}

.upload-content p {
    margin: 0 0 1rem 0;
    color: var(--text-color-light);
}

.file-types {
    display: inline-block;
    background: var(--primary-color);
    color: white;
    padding: 0.25rem 0.75rem;
    border-radius: var(--radius-sm);
    font-size: 0.75rem;
    font-weight: 600;
}

input[type="file"] {
    display: none;
}

/* Preview Container */
.preview-container {
    margin-top: 2rem;
    border: 1px solid var(--border-color);
    border-radius: var(--radius-lg);
    overflow: hidden;
    background: var(--container-background);
    box-shadow: var(--shadow-md);
}

.preview-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem 1.5rem;
    background: var(--background-color);
    border-bottom: 1px solid var(--border-color);
    font-weight: 600;
    color: var(--text-color);
}

.remove-btn {
    background: none;
    border: none;
    color: var(--text-color-light);
    cursor: pointer;
    padding: 0.5rem;
    border-radius: var(--radius-sm);
    transition: all 0.2s ease;
    width: auto;
    margin: 0;
}

.remove-btn:hover {
    background: #fee2e2;
    color: #dc2626;
}

.preview-content {
    position: relative;
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 250px;
    padding: 1rem;
}

#image-preview {
    max-width: 100%;
    max-height: 300px;
    object-fit: contain;
    border-radius: var(--radius-md);
    box-shadow: var(--shadow-md);
    display: none;
}

#image-preview-spinner {
    display: none;
}

/* Options Section */
.options-section {
    padding: 2rem;
    border-top: 1px solid var(--border-color-light);
}

.options-section h4 {
    font-size: 1.125rem;
    font-weight: 600;
    margin: 0 0 1.5rem 0;
    color: var(--text-color);
    display: flex;
    align-items: center;
    gap: 0.75rem;
}

.options-section h4 i {
    color: var(--primary-color);
}

.options-grid {
    display: grid;
    gap: 1.5rem;
}

.option-group {
    display: flex;
    flex-direction: column;
}

.option-label {
    font-weight: 600;
    margin-bottom: 0.75rem;
    color: var(--text-color);
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 0.95rem;
}

.option-label i {
    color: var(--primary-color);
    width: 16px;
}

.custom-select {
    padding: 0.875rem 1rem;
    border-radius: var(--radius-md);
    border: 2px solid var(--border-color);
    background: var(--container-background);
    font-size: 1rem;
    font-weight: 500;
    color: var(--text-color);
    transition: all 0.2s ease;
    cursor: pointer;
    appearance: none;
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
    background-position: right 0.75rem center;
    background-repeat: no-repeat;
    background-size: 1.5em 1.5em;
    padding-right: 3rem;
}

.custom-select:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(79, 70, 229, 0.1);
}

.custom-select:hover {
    border-color: var(--primary-color-light);
}

/* Action Buttons */
.action-buttons {
    padding: 2rem;
    border-top: 1px solid var(--border-color-light);
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.btn-primary {
    background: linear-gradient(135deg, var(--secondary-color), var(--secondary-color-light));
    color: white;
    font-weight: 700;
    padding: 1rem 2rem;
    border: none;
    border-radius: var(--radius-lg);
    cursor: pointer;
    font-size: 1.1rem;
    transition: all 0.3s ease;
    width: 100%;
    margin: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.75rem;
    box-shadow: var(--shadow-md);
    position: relative;
    overflow: hidden;
}

.btn-primary::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s ease;
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-xl);
}

.btn-primary:hover::before {
    left: 100%;
}

.btn-primary:active {
    transform: translateY(0);
}

.btn-secondary {
    background: var(--container-background);
    color: var(--text-color);
    font-weight: 600;
    padding: 0.875rem 1.5rem;
    border: 2px solid var(--border-color);
    border-radius: var(--radius-lg);
    cursor: pointer;
    font-size: 1rem;
    transition: all 0.2s ease;
    width: 100%;
    margin: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
}

.btn-secondary:hover {
    border-color: var(--primary-color);
    color: var(--primary-color);
    background: #f0f4ff;
}

/* Results Section */
.result-container {
    padding: 2rem;
    min-height: 500px;
    display: flex;
    flex-direction: column;
}

.placeholder-content {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    flex: 1;
    text-align: center;
    color: var(--text-color-light);
}

.placeholder-icon {
    font-size: 4rem;
    color: var(--border-color);
    margin-bottom: 1rem;
}

.placeholder-content h4 {
    font-size: 1.25rem;
    font-weight: 600;
    margin: 0 0 0.5rem 0;
    color: var(--text-color);
}

.placeholder-content p {
    margin: 0;
    font-size: 0.95rem;
}

.result-content {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
}

.result-image-container {
    position: relative;
    border-radius: var(--radius-lg);
    overflow: hidden;
    box-shadow: var(--shadow-lg);
}

#result-image {
    width: 100%;
    height: auto;
    display: block;
    border-radius: var(--radius-lg);
}

.image-overlay {
    position: absolute;
    top: 1rem;
    right: 1rem;
    opacity: 0;
    transition: opacity 0.2s ease;
}

.result-image-container:hover .image-overlay {
    opacity: 1;
}

.overlay-btn {
    background: rgba(0, 0, 0, 0.7);
    color: white;
    border: none;
    padding: 0.75rem;
    border-radius: var(--radius-md);
    cursor: pointer;
    transition: all 0.2s ease;
    width: auto;
    margin: 0;
}

.overlay-btn:hover {
    background: rgba(0, 0, 0, 0.9);
}

.result-actions {
    display: flex;
    gap: 1rem;
}

.btn-download {
    flex: 1;
    background: linear-gradient(135deg, var(--primary-color), var(--primary-color-light));
    color: white;
    font-weight: 700;
    padding: 1rem 1.5rem;
    border: none;
    border-radius: var(--radius-lg);
    cursor: pointer;
    font-size: 1rem;
    transition: all 0.3s ease;
    text-decoration: none;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.75rem;
    box-shadow: var(--shadow-md);
}

.btn-download:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-xl);
}

.btn-share {
    background: var(--container-background);
    color: var(--text-color);
    font-weight: 600;
    padding: 1rem 1.5rem;
    border: 2px solid var(--border-color);
    border-radius: var(--radius-lg);
    cursor: pointer;
    font-size: 1rem;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    width: auto;
    margin: 0;
}

.btn-share:hover {
    border-color: var(--primary-color);
    color: var(--primary-color);
    background: #f0f4ff;
}

/* Loading States */
.loading-state {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    flex: 1;
    padding: 3rem 2rem;
    text-align: center;
}

.loading-animation {
    display: flex;
    flex-direction: column;
    align-items: center;
    margin-bottom: 3rem;
}

.spinner-modern {
    width: 60px;
    height: 60px;
    border: 4px solid var(--border-color-light);
    border-top: 4px solid var(--primary-color);
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-bottom: 1.5rem;
}

.spinner {
    width: 40px;
    height: 40px;
    border: 3px solid var(--border-color-light);
    border-top-color: var(--primary-color);
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}

.loading-text h4 {
    font-size: 1.25rem;
    font-weight: 600;
    margin: 0 0 0.5rem 0;
    color: var(--text-color);
}

.loading-text p {
    margin: 0;
    color: var(--text-color-light);
    font-size: 0.95rem;
}

.progress-steps {
    display: flex;
    justify-content: center;
    gap: 2rem;
    margin-top: 2rem;
}

.step {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 0.5rem;
    opacity: 0.4;
    transition: opacity 0.3s ease;
}

.step.active {
    opacity: 1;
}

.step i {
    font-size: 1.5rem;
    color: var(--primary-color);
    background: var(--container-background);
    padding: 1rem;
    border-radius: 50%;
    border: 2px solid var(--border-color);
    width: 3rem;
    height: 3rem;
    display: flex;
    align-items: center;
    justify-content: center;
}

.step.active i {
    background: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
}

.step span {
    font-size: 0.875rem;
    font-weight: 600;
    color: var(--text-color);
}

/* Error Message */
.error-message {
    background: linear-gradient(135deg, #fef2f2, #fee2e2);
    color: #dc2626;
    border: 1px solid #fca5a5;
    padding: 1rem 1.5rem;
    border-radius: var(--radius-lg);
    font-weight: 600;
    margin-top: 1.5rem;
    text-align: center;
    display: none;
    box-shadow: var(--shadow-md);
}

.error-message:not(:empty) {
    display: block;
}

/* Footer */
.footer {
    background: var(--text-color);
    color: white;
    padding: 2rem 0;
    margin-top: 4rem;
}

.footer-content {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 2rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: 1rem;
}

.footer-links {
    display: flex;
    gap: 2rem;
}

.footer-links a {
    color: rgba(255, 255, 255, 0.8);
    text-decoration: none;
    font-weight: 500;
    transition: color 0.2s ease;
}

.footer-links a:hover {
    color: white;
}

/* Responsive Design */
@media (max-width: 1024px) {
    .app-grid {
        gap: 2rem;
    }

    .hero-title {
        font-size: 2.5rem;
    }

    .features-grid {
        grid-template-columns: 1fr;
        gap: 1rem;
    }
}

@media (max-width: 768px) {
    .header-content {
        padding: 0 1rem;
        flex-direction: column;
        gap: 1rem;
    }

    .brand-title {
        font-size: 1.25rem;
    }

    .hero {
        padding: 3rem 0;
    }

    .hero-title {
        font-size: 2rem;
    }

    .hero-subtitle {
        font-size: 1.125rem;
    }

    .main-container {
        padding: 2rem 1rem;
    }

    .app-grid {
        grid-template-columns: 1fr;
        gap: 2rem;
    }

    .section-header {
        padding: 1.5rem 1.5rem 1rem 1.5rem;
    }

    .upload-area, .options-section, .action-buttons, .result-container {
        padding: 1.5rem;
    }

    .file-upload-zone {
        padding: 2rem 1rem;
    }

    .upload-icon {
        font-size: 2.5rem;
    }

    .upload-content h4 {
        font-size: 1.125rem;
    }

    .progress-steps {
        flex-direction: column;
        gap: 1rem;
    }

    .step {
        flex-direction: row;
        justify-content: center;
    }

    .result-actions {
        flex-direction: column;
    }

    .footer-content {
        flex-direction: column;
        text-align: center;
        gap: 1.5rem;
    }

    .footer-links {
        justify-content: center;
    }
}

@media (max-width: 480px) {
    .hero-title {
        font-size: 1.75rem;
    }

    .hero-subtitle {
        font-size: 1rem;
    }

    .section-header h3 {
        font-size: 1.25rem;
    }

    .btn-primary, .btn-download {
        padding: 0.875rem 1.25rem;
        font-size: 0.95rem;
    }

    .file-upload-zone {
        padding: 1.5rem 1rem;
    }

    .upload-content h4 {
        font-size: 1rem;
    }

    .upload-content p {
        font-size: 0.875rem;
    }

    .footer-links {
        flex-direction: column;
        gap: 1rem;
    }
}

/* Accessibility and Focus States */
@media (prefers-reduced-motion: reduce) {
    * {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
    :root {
        --border-color: #000000;
        --text-color-light: #333333;
    }
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
    :root {
        --text-color: #F9FAFB;
        --text-color-light: #D1D5DB;
        --text-color-lighter: #9CA3AF;
        --background-color: #111827;
        --container-background: #1F2937;
        --border-color: #374151;
        --border-color-light: #4B5563;
    }

    .hero {
        background: linear-gradient(135deg, #1F2937 0%, #111827 100%);
    }

    .section-header {
        background: linear-gradient(135deg, #1F2937 0%, #111827 100%);
    }

    .file-upload-zone {
        background: linear-gradient(135deg, #1F2937 0%, #111827 100%);
    }

    .file-upload-zone:hover {
        background: linear-gradient(135deg, #2563EB 0%, #1D4ED8 100%);
    }
}