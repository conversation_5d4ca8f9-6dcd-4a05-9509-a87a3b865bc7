:root {
    --primary-color: #4F46E5;
    --primary-color-light: #6366F1;
    --secondary-color: #10B981;
    --text-color: #1F2937;
    --text-color-light: #6B7280;
    --background-color: #F9FAFB;
    --container-background: #FFFFFF;
    --border-color: #E5E7EB;
}

body {
    font-family: 'Inter', sans-serif;
    display: flex;
    flex-direction: column;
    align-items: center;
    background-color: var(--background-color);
    margin: 0;
    padding: 2rem;
    color: var(--text-color);
}

h1 {
    font-size: 2.25rem;
    font-weight: 700;
    color: var(--text-color);
    margin-bottom: 2rem;
}

.intro-text {
    font-size: 1.125rem;
    color: var(--text-color-light);
    text-align: center;
    max-width: 600px;
    margin-bottom: 2rem;
}

.logo {
    max-width: 150px;
    margin-bottom: 1rem;
}

.container {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 2rem;
    width: 100%;
    max-width: 1000px;
}

.input-container, .output-container {
    background-color: var(--container-background);
    padding: 2rem;
    border-radius: 0.75rem;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    border: 1px solid var(--border-color);
}

h2 {
    font-size: 1.5rem;
    font-weight: 600;
    margin-top: 0;
    margin-bottom: 1.5rem;
}

#camera-section h2 {
    display: none;
}

#capture-btn {
    display: none;
}

input[type="file"] {
    display: none;
}

.custom-file-upload {
    border: 2px solid var(--border-color);
    border-radius: 0.5rem;
    padding: 1.5rem;
    text-align: center;
    cursor: pointer;
    transition: border-color 0.2s ease-in-out, box-shadow 0.2s ease-in-out;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
    display: block; /* Ensure it takes full width */
    margin-bottom: 1rem; /* Space between upload area and preview */
}

.custom-file-upload:hover {
    border-color: var(--primary-color);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.custom-file-upload p {
    margin: 0;
    font-weight: 600;
    color: var(--primary-color);
}

#image-preview-container {
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 200px; /* Give it some height */
    border: 1px solid var(--border-color);
    border-radius: 0.5rem;
    margin-top: 1rem;
    position: relative; /* For absolute positioning of spinner */
}

#image-preview-container .spinner {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
}

#image-preview {
    max-width: 100%;
    max-height: 200px; /* Limit preview height */
    object-fit: contain; /* Ensure image fits within bounds */
    border-radius: 0.5rem;
    display: none; /* Hidden by default, controlled by JS */
}

#image-preview-spinner {
    display: none; /* Hidden by default, controlled by JS */
}

.options {
    margin-top: 1.5rem;
    display: grid;
    gap: 1rem;
}

.options div {
    display: flex;
    flex-direction: column;
}

label {
    font-weight: 500;
    margin-bottom: 0.5rem;
}

select {
    padding: 0.75rem;
    border-radius: 0.375rem;
    border: 1px solid var(--border-color);
    background-color: var(--container-background);
    font-size: 1rem;
    transition: border-color 0.2s ease-in-out, box-shadow 0.2s ease-in-out;
}

select:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(79, 70, 229, 0.3);
}

#image-preview {
    max-width: 100%;
    height: auto;
    border-radius: 0.5rem;
    border: 1px solid var(--border-color);
    margin-top: 1rem;
}

#camera-stream {
    width: 100%;
    height: auto;
    border-radius: 0.5rem;
    border: 1px solid var(--border-color);
}

button {
    background-color: var(--primary-color);
    color: white;
    font-weight: 600;
    padding: 0.75rem 1.5rem;
    border: none;
    border-radius: 0.375rem;
    cursor: pointer;
    font-size: 1rem;
    transition: background-color 0.2s ease-in-out;
    width: 100%;
    margin-top: 1rem;
}

button:hover {
    background-color: var(--primary-color-light);
}

.button {
    display: block;
    background-color: var(--primary-color);
    color: white;
    font-weight: 600;
    padding: 0.75rem 1.5rem;
    border: none;
    border-radius: 0.375rem;
    cursor: pointer;
    font-size: 1rem;
    transition: background-color 0.2s ease-in-out;
    width: 100%;
    margin-top: 1rem;
    text-align: center;
    text-decoration: none;
}

.button:hover {
    background-color: var(--primary-color-light);
}

#use-camera-btn {
    display: none;
}

#capture-btn {
    background-color: var(--secondary-color);
}

#generate-btn {
    background-color: var(--secondary-color);
}

#result-image {
    width: 100%;
    height: auto;
    border-radius: 0.5rem;
    border: 1px solid var(--border-color);
    margin-top: 1rem;
}

#loading {
    display: flex;
    align-items: center;
    justify-content: center;
    flex-direction: column;
    height: 100%;
    font-size: 1.125rem;
    color: var(--text-color-light);
}

.spinner {
    width: 40px;
    height: 40px;
    border: 4px solid var(--border-color);
    border-top-color: var(--primary-color);
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-bottom: 1rem;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}

#error-message {
    color: #EF4444;
    background-color: #FEF2F2;
    border: 1px solid #FCA5A5;
    padding: 0.75rem;
    border-radius: 0.375rem;
    font-weight: 600;
    margin-top: 1rem;
    text-align: center;
}

@media (max-width: 768px) {
    body {
        padding: 1rem;
    }

    .container {
        grid-template-columns: 1fr;
    }

    h1 {
        font-size: 1.875rem;
    }

    .input-container, .output-container {
        padding: 1.5rem;
    }
}